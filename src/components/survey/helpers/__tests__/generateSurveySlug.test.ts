import { generateSurveySlug } from '../generateSurveySlug';

describe('generateSurveySlug', () => {
  describe('Basic functionality', () => {
    it('should convert a simple title to a slug', () => {
      expect(generateSurveySlug('My Survey Title')).toBe('my-survey-title');
    });

    it('should handle empty string', () => {
      expect(generateSurveySlug('')).toBe('');
    });

    it('should handle null input', () => {
      expect(generateSurveySlug(null as any)).toBe('');
    });

    it('should handle undefined input', () => {
      expect(generateSurveySlug(undefined as any)).toBe('');
    });

    it('should handle non-string input', () => {
      expect(generateSurveySlug(123 as any)).toBe('');
    });
  });

  describe('Special characters handling', () => {
    it('should replace spaces with hyphens', () => {
      expect(generateSurveySlug('Survey With Spaces')).toBe('survey-with-spaces');
    });

    it('should remove special characters', () => {
      expect(generateSurveySlug('Survey@#$%^&*()Title')).toBe('survey-title');
    });

    it('should handle multiple consecutive special characters', () => {
      expect(generateSurveySlug('Survey!!!???Title')).toBe('survey-title');
    });

    it('should handle mixed alphanumeric and special characters', () => {
      expect(generateSurveySlug('Survey123!@#Title456')).toBe('survey123-title456');
    });

    it('should preserve numbers', () => {
      expect(generateSurveySlug('Survey 2024 Version 1')).toBe('survey-2024-version-1');
    });
  });

  describe('Edge cases', () => {
    it('should handle leading and trailing spaces', () => {
      expect(generateSurveySlug('  Survey Title  ')).toBe('survey-title');
    });

    it('should handle leading and trailing special characters', () => {
      expect(generateSurveySlug('!!!Survey Title!!!')).toBe('survey-title');
    });

    it('should handle multiple consecutive spaces', () => {
      expect(generateSurveySlug('Survey    Title')).toBe('survey-title');
    });

    it('should handle multiple consecutive hyphens', () => {
      expect(generateSurveySlug('Survey---Title')).toBe('survey-title');
    });

    it('should handle only special characters', () => {
      expect(generateSurveySlug('!@#$%^&*()')).toBe('');
    });

    it('should handle only spaces', () => {
      expect(generateSurveySlug('   ')).toBe('');
    });
  });

  describe('Length limitations', () => {
    it('should limit slug to 50 characters', () => {
      const longTitle = 'This is a very long survey title that exceeds the fifty character limit for URL slugs';
      const slug = generateSurveySlug(longTitle);
      expect(slug.length).toBeLessThanOrEqual(50);
      expect(slug).toBe('this-is-a-very-long-survey-title-that-exceeds-th');
    });

    it('should not end with hyphen after truncation', () => {
      const titleEndingWithSpace = 'This is a very long survey title that ends with ';
      const slug = generateSurveySlug(titleEndingWithSpace);
      expect(slug).not.toMatch(/-$/);
    });

    it('should handle exactly 50 characters', () => {
      const title = 'A'.repeat(50);
      const slug = generateSurveySlug(title);
      expect(slug.length).toBe(50);
      expect(slug).toBe('a'.repeat(50));
    });
  });

  describe('Case sensitivity', () => {
    it('should convert uppercase to lowercase', () => {
      expect(generateSurveySlug('SURVEY TITLE')).toBe('survey-title');
    });

    it('should convert mixed case to lowercase', () => {
      expect(generateSurveySlug('SuRvEy TiTlE')).toBe('survey-title');
    });
  });

  describe('Unicode and international characters', () => {
    it('should handle accented characters', () => {
      expect(generateSurveySlug('Café Survey')).toBe('caf-survey');
    });

    it('should handle unicode characters', () => {
      expect(generateSurveySlug('Survey 🚀 Title')).toBe('survey-title');
    });

    it('should handle non-Latin characters', () => {
      expect(generateSurveySlug('调查问卷')).toBe('');
    });
  });

  describe('Real-world examples', () => {
    it('should handle typical survey titles', () => {
      expect(generateSurveySlug('Customer Satisfaction Survey 2024')).toBe('customer-satisfaction-survey-2024');
      expect(generateSurveySlug('Product Feedback Form')).toBe('product-feedback-form');
      expect(generateSurveySlug('Employee Engagement Survey (Q1)')).toBe('employee-engagement-survey-q1');
      expect(generateSurveySlug('Market Research: Consumer Preferences')).toBe('market-research-consumer-preferences');
    });

    it('should handle survey titles with version numbers', () => {
      expect(generateSurveySlug('Survey v2.1')).toBe('survey-v2-1');
      expect(generateSurveySlug('Beta Test Survey v1.0.0')).toBe('beta-test-survey-v1-0-0');
    });

    it('should handle survey titles with dates', () => {
      expect(generateSurveySlug('Monthly Survey - January 2024')).toBe('monthly-survey-january-2024');
      expect(generateSurveySlug('Q4 2023 Review')).toBe('q4-2023-review');
    });
  });
});
