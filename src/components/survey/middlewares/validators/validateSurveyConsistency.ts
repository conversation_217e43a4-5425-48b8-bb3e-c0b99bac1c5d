import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';
import { generateSurveySlug } from '../../helpers';

/**
 * Middleware to validate survey type and key consistency before sending config
 * Ensures the survey data is consistent and valid
 */
export const validateSurveyConsistency = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const survey = res.locals.survey;
    const providedSlug = req.params.surveySlug;

    if (!survey) {
      console.log(`${Var.app.emoji.failure} Survey not found in res.locals`);
      return res.status(404).json({
        success: false,
        message: 'Survey not found',
      });
    }

    // Validate required survey fields
    if (!survey.public_key || !survey.type || !survey.title) {
      console.log(`${Var.app.emoji.failure} Survey missing required fields`);
      return res.status(500).json({
        success: false,
        message: 'Survey data is incomplete',
      });
    }

    // Validate survey type is one of the allowed types
    const allowedTypes = ['sensePrice', 'senseChoice', 'sensePoll', 'senseQuery', 'sensePriority'];
    if (!allowedTypes.includes(survey.type)) {
      console.log(`${Var.app.emoji.failure} Invalid survey type: ${survey.type}`);
      return res.status(400).json({
        success: false,
        message: 'Invalid survey type',
      });
    }

    // Validate survey is not deleted or disabled
    if (survey.is_deleted) {
      console.log(`${Var.app.emoji.failure} Survey is deleted: ${survey.public_key}`);
      return res.status(404).json({
        success: false,
        message: 'Survey not found',
      });
    }

    if (survey.is_disabled) {
      console.log(`${Var.app.emoji.failure} Survey is disabled: ${survey.public_key}`);
      return res.status(403).json({
        success: false,
        message: 'Survey is not available',
      });
    }

    // Validate survey slug if provided
    if (providedSlug) {
      const expectedSlug = generateSurveySlug(survey.title);
      if (providedSlug !== expectedSlug) {
        console.log(`${Var.app.emoji.failure} Survey slug mismatch. Expected: ${expectedSlug}, Provided: ${providedSlug}`);
        return res.status(400).json({
          success: false,
          message: 'Survey slug does not match survey title',
        });
      }
    }

    // Validate survey config exists and is valid
    if (!survey.config || typeof survey.config !== 'object') {
      console.log(`${Var.app.emoji.failure} Survey config is missing or invalid`);
      return res.status(500).json({
        success: false,
        message: 'Survey configuration is invalid',
      });
    }

    // Validate survey public key format (should be UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(survey.public_key)) {
      console.log(`${Var.app.emoji.failure} Invalid public key format: ${survey.public_key}`);
      return res.status(500).json({
        success: false,
        message: 'Survey data is invalid',
      });
    }

    // Log successful validation in development
    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Survey consistency validation passed for: ${survey.public_key}`);
    }

    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error in validateSurveyConsistency:`, error);
    return res.status(500).json({
      success: false,
      message: 'Error validating survey data',
    });
  }
};
