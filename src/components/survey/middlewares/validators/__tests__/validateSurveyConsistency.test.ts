import { Request, Response, NextFunction } from 'express';
import { validateSurveyConsistency } from '../validateSurveyConsistency';
import { Var } from '../../../../../global/var';

// Mock the Var module
jest.mock('../../../../../global/var', () => ({
  Var: {
    app: {
      emoji: {
        success: '✅',
        failure: '❌',
      },
    },
    node: {
      env: 'test',
    },
  },
}));

// Mock the generateSurveySlug helper
jest.mock('../../helpers', () => ({
  generateSurveySlug: jest.fn((title: string) => {
    return title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
  }),
}));

describe('validateSurveyConsistency', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {
      params: {},
    };
    mockResponse = {
      locals: {},
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };
    mockNext = jest.fn();
    
    // Reset console.log mock
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Survey validation', () => {
    it('should pass validation for a valid survey', async () => {
      const validSurvey = {
        public_key: '123e4567-e89b-12d3-a456-************',
        type: 'sensePoll',
        title: 'Test Survey',
        config: { question: 'Test question' },
        is_deleted: false,
        is_disabled: false,
      };

      mockResponse.locals!.survey = validSurvey;

      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    it('should fail when survey is not found in res.locals', async () => {
      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Survey not found',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should fail when survey is missing required fields', async () => {
      const incompleteSurvey = {
        public_key: '123e4567-e89b-12d3-a456-************',
        // Missing type and title
        config: { question: 'Test question' },
      };

      mockResponse.locals!.survey = incompleteSurvey;

      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Survey data is incomplete',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Survey type validation', () => {
    const allowedTypes = ['sensePrice', 'senseChoice', 'sensePoll', 'senseQuery', 'sensePriority'];

    allowedTypes.forEach(type => {
      it(`should pass validation for allowed type: ${type}`, async () => {
        const survey = {
          public_key: '123e4567-e89b-12d3-a456-************',
          type,
          title: 'Test Survey',
          config: { question: 'Test question' },
          is_deleted: false,
          is_disabled: false,
        };

        mockResponse.locals!.survey = survey;

        await validateSurveyConsistency(
          mockRequest as Request,
          mockResponse as Response,
          mockNext
        );

        expect(mockNext).toHaveBeenCalled();
        expect(mockResponse.status).not.toHaveBeenCalled();
      });
    });

    it('should fail for invalid survey type', async () => {
      const survey = {
        public_key: '123e4567-e89b-12d3-a456-************',
        type: 'invalidType',
        title: 'Test Survey',
        config: { question: 'Test question' },
        is_deleted: false,
        is_disabled: false,
      };

      mockResponse.locals!.survey = survey;

      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Invalid survey type',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Survey status validation', () => {
    it('should fail for deleted survey', async () => {
      const survey = {
        public_key: '123e4567-e89b-12d3-a456-************',
        type: 'sensePoll',
        title: 'Test Survey',
        config: { question: 'Test question' },
        is_deleted: true,
        is_disabled: false,
      };

      mockResponse.locals!.survey = survey;

      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Survey not found',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should fail for disabled survey', async () => {
      const survey = {
        public_key: '123e4567-e89b-12d3-a456-************',
        type: 'sensePoll',
        title: 'Test Survey',
        config: { question: 'Test question' },
        is_deleted: false,
        is_disabled: true,
      };

      mockResponse.locals!.survey = survey;

      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Survey is not available',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Survey slug validation', () => {
    it('should pass when no slug is provided', async () => {
      const survey = {
        public_key: '123e4567-e89b-12d3-a456-************',
        type: 'sensePoll',
        title: 'Test Survey',
        config: { question: 'Test question' },
        is_deleted: false,
        is_disabled: false,
      };

      mockResponse.locals!.survey = survey;

      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    it('should pass when provided slug matches expected slug', async () => {
      const survey = {
        public_key: '123e4567-e89b-12d3-a456-************',
        type: 'sensePoll',
        title: 'Test Survey',
        config: { question: 'Test question' },
        is_deleted: false,
        is_disabled: false,
      };

      mockRequest.params!.surveySlug = 'test-survey';
      mockResponse.locals!.survey = survey;

      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    it('should fail when provided slug does not match expected slug', async () => {
      const survey = {
        public_key: '123e4567-e89b-12d3-a456-************',
        type: 'sensePoll',
        title: 'Test Survey',
        config: { question: 'Test question' },
        is_deleted: false,
        is_disabled: false,
      };

      mockRequest.params!.surveySlug = 'wrong-slug';
      mockResponse.locals!.survey = survey;

      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Survey slug does not match survey title',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Survey config validation', () => {
    it('should fail when config is missing', async () => {
      const survey = {
        public_key: '123e4567-e89b-12d3-a456-************',
        type: 'sensePoll',
        title: 'Test Survey',
        is_deleted: false,
        is_disabled: false,
      };

      mockResponse.locals!.survey = survey;

      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Survey configuration is invalid',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should fail when config is not an object', async () => {
      const survey = {
        public_key: '123e4567-e89b-12d3-a456-************',
        type: 'sensePoll',
        title: 'Test Survey',
        config: 'invalid config',
        is_deleted: false,
        is_disabled: false,
      };

      mockResponse.locals!.survey = survey;

      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Survey configuration is invalid',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Public key validation', () => {
    it('should fail for invalid UUID format', async () => {
      const survey = {
        public_key: 'invalid-uuid',
        type: 'sensePoll',
        title: 'Test Survey',
        config: { question: 'Test question' },
        is_deleted: false,
        is_disabled: false,
      };

      mockResponse.locals!.survey = survey;

      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Survey data is invalid',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should pass for valid UUID format', async () => {
      const survey = {
        public_key: '123e4567-e89b-12d3-a456-************',
        type: 'sensePoll',
        title: 'Test Survey',
        config: { question: 'Test question' },
        is_deleted: false,
        is_disabled: false,
      };

      mockResponse.locals!.survey = survey;

      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.status).not.toHaveBeenCalled();
    });
  });

  describe('Error handling', () => {
    it('should handle unexpected errors gracefully', async () => {
      // Mock generateSurveySlug to throw an error
      const { generateSurveySlug } = require('../../helpers');
      generateSurveySlug.mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      const survey = {
        public_key: '123e4567-e89b-12d3-a456-************',
        type: 'sensePoll',
        title: 'Test Survey',
        config: { question: 'Test question' },
        is_deleted: false,
        is_disabled: false,
      };

      mockRequest.params!.surveySlug = 'test-slug';
      mockResponse.locals!.survey = survey;

      await validateSurveyConsistency(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Error validating survey data',
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
