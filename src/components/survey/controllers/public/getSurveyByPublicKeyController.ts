import { Request, Response } from 'express';
import { Var } from '../../../../global/var';
import { generateSurveySlug } from '../../helpers';

export const getSurveyByPublicKeyController = async (req: Request, res: Response) => {
  const survey = res.locals.survey;

  // Generate the survey slug from the title
  const surveySlug = generateSurveySlug(survey.title);

  let surveyObj: any = {
    config: survey.config,
    respondentDetails: survey.respondent_details,
    type: survey.type,
    publicKey: survey.public_key,
    title: survey.title,
    slug: surveySlug,
  };

  return res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} Survey fetched by public key`,
    payload: surveyObj,
  });
};
