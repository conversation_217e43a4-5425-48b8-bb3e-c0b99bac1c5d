import { Request, Response } from 'express';
import { Var } from '../../../../global/var';
import { generateSurveySlug } from '../../helpers';

export const getSurveyByPublicKeyController = async (req: Request, res: Response) => {
  const survey = res.locals.survey;
  const providedSlug = req.params.surveySlug;

  // Generate the expected slug from the survey title
  const expectedSlug = generateSurveySlug(survey.title);

  // If a slug was provided in the URL, validate it matches the survey title
  if (providedSlug && providedSlug !== expectedSlug) {
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} Survey slug does not match survey title`,
    });
  }

  let surveyObj: any = {
    config: survey.config,
    respondentDetails: survey.respondent_details,
    type: survey.type,
    publicKey: survey.public_key,
    title: survey.title,
    slug: expectedSlug,
  };

  return res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} Survey fetched by public key`,
    payload: surveyObj,
  });
};
