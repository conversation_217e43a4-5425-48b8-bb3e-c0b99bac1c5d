import { Request, Response } from 'express';
import { Var } from '../../../../global/var';
import { generateEmbedCode, generateSurveySlug } from '../../helpers';

export const getEmbedCodeController = async (_req: Request, res: Response) => {
  const survey = res.locals.survey;

  try {
    // Generate the embed code and survey slug
    const embedCode = generateEmbedCode(survey.type, survey.public_key);
    const surveySlug = generateSurveySlug(survey.title);

    return res.status(200).json({
      success: true,
      message: `${Var.app.emoji.success} Embed code fetched`,
      payload: {
        embedCode: embedCode,
        publicKey: survey.public_key,
        type: survey.type,
        title: survey.title,
        slug: surveySlug,
      },
    });
  } catch (error) {
    console.error(`Error generating embed code: ${error}`);
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} Could not generate embed code`,
    });
  }
};
