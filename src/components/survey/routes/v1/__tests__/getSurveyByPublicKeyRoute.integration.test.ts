import request from 'supertest';
import express from 'express';
import { getSurveyByPublicKeyRoute } from '../getSurveyByPublicKeyRoute';

// Mock all the middlewares and controllers
jest.mock('../../../../global/middlewares', () => ({
  ExtractOriginFromRequest: jest.fn((req, res, next) => {
    res.locals.origin = 'http://localhost:3000';
    next();
  }),
  ExtractIPAddressFromOrigin: jest.fn((req, res, next) => {
    res.locals.ipAddress = '127.0.0.1';
    next();
  }),
  ExtractCountryFromIPAddress: jest.fn((req, res, next) => {
    res.locals.country = 'US';
    next();
  }),
}));

jest.mock('../../middlewares', () => ({
  validateGetSurveyByPublicKeyPayload: jest.fn((req, res, next) => next()),
  getSurveyFromCache: jest.fn((req, res, next) => next()),
  BlockRequestByDistribution: jest.fn((req, res, next) => {
    // Mock survey data
    res.locals.survey = {
      public_key: req.params.publicKey,
      title: 'Test Survey',
      type: 'sensePoll',
      config: { question: 'Test question' },
      respondent_details: [],
      is_deleted: false,
      is_disabled: false,
    };
    next();
  }),
  validateSurveyConsistency: jest.fn((req, res, next) => next()),
}));

jest.mock('../../controllers', () => ({
  getSurveyByPublicKeyController: jest.fn((req, res) => {
    const survey = res.locals.survey;
    const slug = survey.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
    
    res.status(200).json({
      success: true,
      message: '✅ Survey fetched by public key',
      payload: {
        config: survey.config,
        respondentDetails: survey.respondent_details,
        type: survey.type,
        publicKey: survey.public_key,
        title: survey.title,
        slug: slug,
      },
    });
  }),
}));

jest.mock('../../../../global/helpers', () => ({
  GenerateApiVersionPath: jest.fn(() => '/v1/'),
}));

describe('getSurveyByPublicKeyRoute Integration Tests', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    app.use(getSurveyByPublicKeyRoute);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /v1/public-surveys/:publicKey', () => {
    it('should successfully fetch survey without slug', async () => {
      const publicKey = '123e4567-e89b-12d3-a456-************';
      
      const response = await request(app)
        .get(`/v1/public-surveys/${publicKey}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: '✅ Survey fetched by public key',
        payload: {
          config: { question: 'Test question' },
          respondentDetails: [],
          type: 'sensePoll',
          publicKey: publicKey,
          title: 'Test Survey',
          slug: 'test-survey',
        },
      });
    });

    it('should successfully fetch survey with matching slug', async () => {
      const publicKey = '123e4567-e89b-12d3-a456-************';
      const slug = 'test-survey';
      
      const response = await request(app)
        .get(`/v1/public-surveys/${publicKey}/${slug}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: '✅ Survey fetched by public key',
        payload: {
          config: { question: 'Test question' },
          respondentDetails: [],
          type: 'sensePoll',
          publicKey: publicKey,
          title: 'Test Survey',
          slug: 'test-survey',
        },
      });
    });

    it('should handle invalid UUID format', async () => {
      const invalidKey = 'invalid-uuid';
      
      // Mock validation to fail for invalid UUID
      const { validateGetSurveyByPublicKeyPayload } = require('../../middlewares');
      validateGetSurveyByPublicKeyPayload.mockImplementationOnce((req, res, next) => {
        res.status(400).json({
          success: false,
          message: 'Invalid public key format',
        });
      });

      await request(app)
        .get(`/v1/public-surveys/${invalidKey}`)
        .expect(400);
    });

    it('should handle survey not found', async () => {
      const publicKey = '123e4567-e89b-12d3-a456-************';
      
      // Mock BlockRequestByDistribution to not set survey
      const { BlockRequestByDistribution } = require('../../middlewares');
      BlockRequestByDistribution.mockImplementationOnce((req, res, next) => {
        res.status(404).json({
          success: false,
          message: 'Survey not found',
        });
      });

      await request(app)
        .get(`/v1/public-surveys/${publicKey}`)
        .expect(404);
    });

    it('should handle slug mismatch', async () => {
      const publicKey = '123e4567-e89b-12d3-a456-************';
      const wrongSlug = 'wrong-slug';
      
      // Mock validateSurveyConsistency to fail for wrong slug
      const { validateSurveyConsistency } = require('../../middlewares');
      validateSurveyConsistency.mockImplementationOnce((req, res, next) => {
        res.status(400).json({
          success: false,
          message: 'Survey slug does not match survey title',
        });
      });

      await request(app)
        .get(`/v1/public-surveys/${publicKey}/${wrongSlug}`)
        .expect(400);
    });
  });

  describe('Route parameter validation', () => {
    it('should accept valid UUID for publicKey', async () => {
      const validUUIDs = [
        '123e4567-e89b-12d3-a456-************',
        '987fcdeb-51a2-43d1-9f12-123456789abc',
        '456e7890-e12b-34d5-a678-************',
      ];

      for (const uuid of validUUIDs) {
        await request(app)
          .get(`/v1/public-surveys/${uuid}`)
          .expect(200);
      }
    });

    it('should accept valid slugs', async () => {
      const publicKey = '123e4567-e89b-12d3-a456-************';
      const validSlugs = [
        'test-survey',
        'customer-satisfaction-survey-2024',
        'product-feedback-form',
        'employee-engagement-q1',
        'market-research-consumer-preferences',
      ];

      for (const slug of validSlugs) {
        await request(app)
          .get(`/v1/public-surveys/${publicKey}/${slug}`)
          .expect(200);
      }
    });

    it('should handle empty slug gracefully', async () => {
      const publicKey = '123e4567-e89b-12d3-a456-************';
      
      await request(app)
        .get(`/v1/public-surveys/${publicKey}/`)
        .expect(200);
    });
  });

  describe('Middleware execution order', () => {
    it('should call middlewares in correct order', async () => {
      const publicKey = '123e4567-e89b-12d3-a456-************';
      
      const {
        ExtractOriginFromRequest,
        ExtractIPAddressFromOrigin,
        ExtractCountryFromIPAddress,
      } = require('../../../../global/middlewares');
      
      const {
        validateGetSurveyByPublicKeyPayload,
        getSurveyFromCache,
        BlockRequestByDistribution,
        validateSurveyConsistency,
      } = require('../../middlewares');
      
      const { getSurveyByPublicKeyController } = require('../../controllers');

      await request(app)
        .get(`/v1/public-surveys/${publicKey}`)
        .expect(200);

      // Verify middleware execution order
      expect(ExtractOriginFromRequest).toHaveBeenCalled();
      expect(ExtractIPAddressFromOrigin).toHaveBeenCalled();
      expect(ExtractCountryFromIPAddress).toHaveBeenCalled();
      expect(validateGetSurveyByPublicKeyPayload).toHaveBeenCalled();
      expect(getSurveyFromCache).toHaveBeenCalled();
      expect(BlockRequestByDistribution).toHaveBeenCalled();
      expect(validateSurveyConsistency).toHaveBeenCalled();
      expect(getSurveyByPublicKeyController).toHaveBeenCalled();
    });
  });

  describe('Response format validation', () => {
    it('should return consistent response structure', async () => {
      const publicKey = '123e4567-e89b-12d3-a456-************';
      
      const response = await request(app)
        .get(`/v1/public-surveys/${publicKey}`)
        .expect(200);

      // Validate response structure
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('payload');
      
      const payload = response.body.payload;
      expect(payload).toHaveProperty('config');
      expect(payload).toHaveProperty('respondentDetails');
      expect(payload).toHaveProperty('type');
      expect(payload).toHaveProperty('publicKey');
      expect(payload).toHaveProperty('title');
      expect(payload).toHaveProperty('slug');
    });

    it('should return proper content type', async () => {
      const publicKey = '123e4567-e89b-12d3-a456-************';
      
      await request(app)
        .get(`/v1/public-surveys/${publicKey}`)
        .expect('Content-Type', /json/)
        .expect(200);
    });
  });

  describe('Error handling', () => {
    it('should handle middleware errors gracefully', async () => {
      const publicKey = '123e4567-e89b-12d3-a456-************';
      
      // Mock a middleware to throw an error
      const { getSurveyFromCache } = require('../../middlewares');
      getSurveyFromCache.mockImplementationOnce((req, res, next) => {
        const error = new Error('Cache error');
        next(error);
      });

      // Note: This would need proper error handling middleware in a real app
      // For now, we expect the error to be thrown
      await request(app)
        .get(`/v1/public-surveys/${publicKey}`)
        .expect(500);
    });
  });
});
